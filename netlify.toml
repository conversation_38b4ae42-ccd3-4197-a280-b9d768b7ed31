# Netlify configuration for Naroop React/Vite application
[build]
  # Build command - install dependencies and build
  command = "npm ci && npm run build"

  # Publish directory - where Vite outputs the built files
  publish = "dist"

  # Functions directory (if you plan to add Netlify Functions later)
  functions = "netlify/functions"

# Build environment variables
[build.environment]
  # Node.js version for build environment
  NODE_VERSION = "18"

  # Ensure we're using the latest npm
  NPM_FLAGS = "--version"

  # Set production environment
  NODE_ENV = "production"

# SPA routing - redirect all routes to index.html for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false

# Security headers for enhanced security
[[headers]]
  for = "/*"
  [headers.values]
    # Prevent clickjacking
    X-Frame-Options = "DENY"
    
    # Prevent MIME type sniffing
    X-Content-Type-Options = "nosniff"
    
    # Enable XSS protection
    X-XSS-Protection = "1; mode=block"
    
    # Referrer policy
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed for your Firebase integration)
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.googleapis.com https://*.firebase.com https://*.firebaseapp.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.googleapis.com https://*.firebase.com https://*.firebaseapp.com wss://*.firebaseio.com;"

# Cache static assets for better performance
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache service worker
[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Build plugins for optimization
[[plugins]]
  package = "@netlify/plugin-lighthouse"
  
  [plugins.inputs.thresholds]
    performance = 0.8
    accessibility = 0.9
    best-practices = 0.8
    seo = 0.8

# Environment-specific settings
[context.production]
  command = "npm ci && npm run build"

[context.deploy-preview]
  command = "npm ci && npm run build"

[context.branch-deploy]
  command = "npm ci && npm run build"

# Functions directory (if you plan to add Netlify Functions later)
# Note: Functions configuration moved to main [build] section above
